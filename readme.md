# App  
FindAFriend API

## Application Rules
[x] It must be possible to register as an ORG  
[x] It must be possible to log in as an ORG  
[x] It must be possible to register a pet
[x] It must be possible to list all pets available for adoption in a city  
[x] It must be possible to filter pets by their characteristics  
[x] It must be possible to view details of a pet available for adoption

## Business Rules  
[x] To list pets, the city must be provided  
[x] An ORG must have an address and a WhatsApp number  
[x] A pet must be associated with an ORG  
[] The user who wants to adopt will contact the ORG via WhatsApp  
[x] All filters, except for the city, are optional
[] For an ORG to access the application as admin, it must be logged in