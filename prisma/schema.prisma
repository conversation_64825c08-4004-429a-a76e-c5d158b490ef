// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Size {
  SMALL
  MEDIUM
  LARGE
}

enum EnergyLevel {
  LOW
  MEDIUM
  HIGH
}

enum DependencyLevel {
  LOW
  MEDIUM
  HIGH
}

model Org {
  id            String @id @default(uuid())
  name          String
  owner_name    String
  email         String @unique
  password_hash String
  whatsapp      String

  cep          String
  state        String
  city         String
  neighborhood String
  street       String

  latitude  Decimal
  longitude Decimal

  pets Pet[]

  @@map("orgs")
}

model Pet {
  id               String          @id @default(uuid())
  name             String
  description      String
  age              String
  size             String
  energy_level     String
  dependency_level DependencyLevel @default(MEDIUM)
  org_id           String

  created_at DateTime @default(now())

  org Org @relation(fields: [org_id], references: [id])

  @@map("pets")
}
