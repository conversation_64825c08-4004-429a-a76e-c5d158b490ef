import { defineConfig } from 'vitest/config'
import tsconfigPaths from 'vite-tsconfig-paths'

export default defineConfig({
  test: {
    dir: 'src',
    projects: [
      {
        extends: true,
        test: {
          name: 'unit',
          dir: 'src/use-cases',
        },
      },
      {
        extends: true,
        test: {
          name: 'e2e',
          dir: 'src/http/controllers',
          environment:
            './prisma/vitest-environment-prisma/prisma-test-environment.ts',
        },
      },
    ],
    coverage: {
      exclude: ['prisma', 'generated'],
      include: [
        'src/use-cases/**',
        'src/errors/**',
        'src/repositories/in-memory/**',
      ],
    },
  },

  plugins: [tsconfigPaths()],
})
