import z from 'zod'
import type { FastifyReply, FastifyRequest } from 'fastify'

import { makeRegisterOrgUseCase } from '@/use-cases/factories/makeRegisterOrgUseCase'
import { OrgAlreadyRegisteredError } from '@/errors/org-already-registered'

export async function create(request: FastifyRequest, reply: FastifyReply) {
  const createOrgBodySchema = z.object({
    name: z.string(),
    owner_name: z.string(),
    email: z.email(),
    password: z.string(),
    state: z.string(),
    cep: z.string(),
    city: z.string(),
    neighborhood: z.string(),
    street: z.string(),
    latitude: z.number(),
    longitude: z.number(),
    whatsapp: z.string(),
  })

  const {
    name,
    owner_name,
    email,
    password,
    state,
    cep,
    city,
    neighborhood,
    street,
    latitude,
    longitude,
    whatsapp,
  } = createOrgBodySchema.parse(request.body)

  try {
    const registerOrgUseCase = makeRegisterOrgUseCase()

    await registerOrgUseCase.execute({
      name,
      owner_name,
      email,
      state,
      password,
      cep,
      city,
      neighborhood,
      street,
      latitude,
      longitude,
      whatsapp,
    })
  } catch (error) {
    console.log('error: ', error)
    if (error instanceof OrgAlreadyRegisteredError) {
      return reply.status(409).send({
        message: error.message,
      })
    }

    throw error
  }

  return reply.status(201).send()
}
