import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import request from 'supertest'

import { app } from '@/app'
import { makeOrg } from '@/tests/factories/make-org-factory'

describe('Create orgs (e2e)', () => {
  beforeAll(async () => {
    await app.ready()
  })

  afterAll(async () => {
    await app.close()
  })

  it('should be able to create a org', async () => {
    const fakeOrg = makeOrg()

    const response = await request(app.server).post('/orgs').send({ fakeOrg })

    expect(response.statusCode).toBe(201)
  })
})
