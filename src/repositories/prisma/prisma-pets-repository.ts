import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'generated/prisma'

import { prisma } from '@/lib/prisma'

import type { FindManyParams, PetsRepository } from '../pets-repository'

export class PrismaPetsRepository implements PetsRepository {
  async create(data: Prisma.PetUncheckedCreateInput): Promise<Pet> {
    const pet = await prisma.pet.create({
      data,
    })

    return pet
  }

  async findAllByCity(city: string): Promise<Pet[]> {
    const pets = await prisma.pet.findMany({
      where: {
        org: {
          city,
        },
      },
    })

    return pets
  }

  async findMany({
    age,
    energyLevel,
    size,
    dependencyLevel,
  }: FindManyParams): Promise<Pet[]> {
    const pets = await prisma.pet.findMany({
      where: {
        age,
        energy_level: energyLevel,
        size,
        dependency_level: dependencyLevel,
      },
    })

    return pets
  }

  async findById(id: string): Promise<Pet | null> {
    const pet = await prisma.pet.findUnique({
      where: {
        id,
      },
    })

    return pet
  }
}
