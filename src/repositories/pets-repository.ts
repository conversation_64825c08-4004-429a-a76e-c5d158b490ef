import type {
  DependencyLevel,
  EnergyLevel,
  Pet,
  Prisma,
  Si<PERSON>,
} from 'generated/prisma'

export interface FindManyParams {
  age?: string
  energyLevel?: EnergyLevel
  size?: Size
  dependencyLevel?: DependencyLevel
}

export interface PetsRepository {
  create(data: Prisma.PetUncheckedCreateInput): Promise<Pet>

  findAllByCity(city: string): Promise<Pet[]>

  findMany({
    age,
    energyLevel,
    size,
    dependencyLevel,
  }: FindManyParams): Promise<Pet[]>

  findById(id: string): Promise<Pet | null>
}
