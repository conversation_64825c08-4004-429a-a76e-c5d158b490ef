import { Prisma, Org } from 'generated/prisma'
import { randomUUID } from 'node:crypto'

import { getDistanceBetweenCoordinates } from '@/utils/getDistanceBetweenCoordinates'

import type { FindManyNearbyParams, OrgsRepository } from '../orgs-repository'

export class InMemoryOrgsRepository implements OrgsRepository {
  public orgs: Org[]

  constructor() {
    this.orgs = []
  }

  async findManyNearby({
    latitude,
    longitude,
  }: FindManyNearbyParams): Promise<Org[]> {
    const orgs = this.orgs.filter((org) => {
      const distance = getDistanceBetweenCoordinates(
        {
          latitude,
          longitude,
        },
        {
          latitude: org.latitude.toNumber(),
          longitude: org.longitude.toNumber(),
        },
      )

      return distance < 10
    })

    return orgs
  }

  async findById(id: string): Promise<Org | null> {
    const org = this.orgs.find((item) => item.id === id)

    if (!org) {
      return null
    }

    return org
  }

  async findByEmail(email: string): Promise<Org | null> {
    const org = this.orgs.find((org) => org.email === email)

    if (!org) {
      return null
    }

    return org
  }

  async create(data: Prisma.OrgCreateInput): Promise<Org> {
    const org: Org = {
      id: data.id ?? randomUUID(),
      name: data.name,
      email: data.email,
      password_hash: data.password_hash,
      owner_name: data.owner_name,
      whatsapp: data.whatsapp,

      cep: data.cep,
      city: data.city,
      state: data.state,
      neighborhood: data.neighborhood,
      street: data.street,
      latitude: new Prisma.Decimal(data.latitude.toString()),
      longitude: new Prisma.Decimal(data.longitude.toString()),
    }

    this.orgs.push(org)

    return org
  }
}
