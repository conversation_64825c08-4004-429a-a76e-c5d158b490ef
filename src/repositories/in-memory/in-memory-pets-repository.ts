import type { <PERSON><PERSON><PERSON>, Pet } from 'generated/prisma'
import { randomUUID } from 'node:crypto'

import type { FindManyParams, PetsRepository } from '../pets-repository'
import type { InMemoryOrgsRepository } from './in-memory-orgs-repository'

export class InMemoryPetsRepository implements PetsRepository {
  public pets: Pet[]

  constructor(private orgsRepository: InMemoryOrgsRepository) {
    this.pets = []
  }

  async findById(id: string): Promise<Pet | null> {
    const pet = this.pets.find((item) => item.id === id)

    if (!pet) {
      return null
    }

    return pet
  }

  async findMany({
    age,
    energyLevel,
    size,
    dependencyLevel,
  }: FindManyParams): Promise<Pet[]> {
    const pets = this.pets
      .filter((pet) => pet.age === age)
      .filter((pet) => pet.energy_level === energyLevel)
      .filter((pet) => pet.size === size)
      .filter((pet) => pet.dependency_level === dependencyLevel)

    return pets
  }

  async findAllByCity(city: string): Promise<Pet[]> {
    const orgByCity = this.orgsRepository.orgs.find((org) => org.city === city)

    const pets = this.pets.filter((pet) => pet.org_id === orgByCity?.id)

    return pets
  }

  async create({
    id,
    name,
    description,
    age,
    dependency_level,
    energy_level,
    org_id,
    size,
  }: Prisma.PetUncheckedCreateInput): Promise<Pet> {
    const pet: Pet = {
      id: id ?? randomUUID(),
      name,
      description,
      dependency_level: dependency_level ?? 'MEDIUM',
      age,
      energy_level,
      org_id,
      size,
      created_at: new Date(),
    }

    this.pets.push(pet)

    return pet
  }
}
