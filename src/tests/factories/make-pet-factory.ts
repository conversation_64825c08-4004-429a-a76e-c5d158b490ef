import { faker } from '@faker-js/faker'
import type { DependencyLevel, EnergyLevel, Size } from 'generated/prisma'
import crypto from 'node:crypto'

type Overwrite = {
  org_id?: string
  age?: string
  size?: Size
  energy_level?: EnergyLevel
  dependency_level?: DependencyLevel
}

export function makePet(overwrite?: Overwrite) {
  return {
    id: crypto.randomUUID(),
    org_id: overwrite?.org_id ?? crypto.randomUUID(),
    name: faker.animal.dog(),
    description: faker.lorem.paragraph(),
    dependency_level:
      overwrite?.dependency_level ??
      faker.helpers.arrayElement(['LOW', 'MEDIUM', 'HIGH']),
    age: overwrite?.age ?? faker.number.int().toString(),
    size:
      overwrite?.size ??
      faker.helpers.arrayElement(['SMALL', 'MEDIUM', 'LARGE']),
    energy_level:
      overwrite?.energy_level ??
      faker.helpers.arrayElement(['LOW', 'MEDIUM', 'HIGH']),
  }
}
