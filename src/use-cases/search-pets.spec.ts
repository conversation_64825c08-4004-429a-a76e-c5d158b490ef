import { beforeEach, describe, expect, it } from 'vitest'
import { hash } from 'bcryptjs'

import { InMemoryPetsRepository } from '@/repositories/in-memory/in-memory-pets-repository'
import { InMemoryOrgsRepository } from '@/repositories/in-memory/in-memory-orgs-repository'

import { makeOrg } from '@/tests/factories/make-org-factory'
import { makePet } from '@/tests/factories/make-pet-factory'

import { SearchPetsUseCase } from './search-pets'

describe('Search pets use-case', () => {
  let orgsRepository: InMemoryOrgsRepository
  let petsRepository: InMemoryPetsRepository
  let sut: SearchPetsUseCase

  beforeEach(() => {
    orgsRepository = new InMemoryOrgsRepository()
    petsRepository = new InMemoryPetsRepository(orgsRepository)
    sut = new SearchPetsUseCase(petsRepository)
  })

  it('should be able to search pets by their characteristics  ', async () => {
    const {
      name,
      email,
      owner_name,
      cep,
      city,
      latitude,
      longitude,
      neighborhood,
      state,
      street,
      whatsapp,
      password,
    } = makeOrg({
      city: 'Itapevi',
    })

    const org = await orgsRepository.create({
      name,
      email,
      owner_name,
      cep,
      city,
      latitude,
      longitude,
      neighborhood,
      state,
      street,
      whatsapp,
      password_hash: await hash(password, 6),
    })

    await petsRepository.create({
      ...makePet({
        org_id: org.id,
        age: 'filhote',
        energy_level: 'LOW',
        dependency_level: 'MEDIUM',
        size: 'SMALL',
      }),
    })

    const { pets } = await sut.execute({
      age: 'filhote',
      energyLevel: 'LOW',
      dependencyLevel: 'MEDIUM',
      size: 'SMALL',
    })

    expect(pets).toHaveLength(1)
    expect(pets).toEqual([
      expect.objectContaining({
        id: expect.any(String),
        org_id: org.id,
        age: 'filhote',
        energy_level: 'LOW',
        dependency_level: 'MEDIUM',
        size: 'SMALL',
      }),
    ])
  })
})
