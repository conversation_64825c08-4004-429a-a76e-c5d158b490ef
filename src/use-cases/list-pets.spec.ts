import { beforeEach, describe, expect, it } from 'vitest'
import { hash } from 'bcryptjs'

import { InMemoryPetsRepository } from '@/repositories/in-memory/in-memory-pets-repository'
import { InMemoryOrgsRepository } from '@/repositories/in-memory/in-memory-orgs-repository'

import { makeOrg } from '@/tests/factories/make-org-factory'
import { makePet } from '@/tests/factories/make-pet-factory'

import { ListPetsUseCase } from './list-pets'

describe('List pets use-case', () => {
  let orgsRepository: InMemoryOrgsRepository
  let petsRepository: InMemoryPetsRepository
  let sut: ListPetsUseCase

  beforeEach(() => {
    orgsRepository = new InMemoryOrgsRepository()
    petsRepository = new InMemoryPetsRepository(orgsRepository)
    sut = new ListPetsUseCase(petsRepository)
  })

  it('should be able to list pets by city', async () => {
    const {
      name,
      email,
      owner_name,
      cep,
      city,
      latitude,
      longitude,
      neighborhood,
      state,
      street,
      whatsapp,
      password,
    } = makeOrg({
      city: 'Itapevi',
    })

    const org = await orgsRepository.create({
      name,
      email,
      owner_name,
      cep,
      city,
      latitude,
      longitude,
      neighborhood,
      state,
      street,
      whatsapp,
      password_hash: await hash(password, 6),
    })

    await petsRepository.create({
      ...makePet({
        org_id: org.id,
      }),
    })

    const { pets } = await sut.execute({
      city: 'Itapevi',
    })

    expect(pets).toHaveLength(1)
    expect(pets).toEqual([
      expect.objectContaining({
        id: expect.any(String),
      }),
    ])
  })
})
