import type { DependencyLevel, EnergyLevel, Pet, Size } from 'generated/prisma'

import type { PetsRepository } from '@/repositories/pets-repository'

interface SearchPetsUseCaseRequest {
  age?: string
  energyLevel?: EnergyLevel
  size?: Size
  dependencyLevel?: DependencyLevel
}

interface SearchPetsUseCaseResponse {
  pets: Pet[]
}

export class SearchPetsUseCase {
  // eslint-disable-next-line prettier/prettier
  constructor(private petsRepository: PetsRepository) { }

  async execute({
    age,
    energyLevel,
    size,
    dependencyLevel,
  }: SearchPetsUseCaseRequest): Promise<SearchPetsUseCaseResponse> {
    const pets = await this.petsRepository.findMany({
      age,
      energyLevel,
      size,
      dependencyLevel,
    })

    return {
      pets,
    }
  }
}
