import type { Pet } from 'generated/prisma'

import type { PetsRepository } from '@/repositories/pets-repository'
import { ResourceNotFoundError } from '@/errors/resource-not-found-error'

interface GetPetDetailsUseCaseRequest {
  petId: string
}

interface GetPetDetailsUseCaseResponse {
  pet: Pet
}

export class GetPetDetailsUseCase {
  // eslint-disable-next-line prettier/prettier
  constructor(private petsRepository: PetsRepository) { }

  async execute({
    petId,
  }: GetPetDetailsUseCaseRequest): Promise<GetPetDetailsUseCaseResponse> {
    const pet = await this.petsRepository.findById(petId)

    if (!pet) {
      throw new ResourceNotFoundError()
    }

    return {
      pet,
    }
  }
}
