import { beforeEach, describe, expect, it } from 'vitest'
import { hash } from 'bcryptjs'

import { InMemoryOrgsRepository } from '@/repositories/in-memory/in-memory-orgs-repository'
import { InMemoryPetsRepository } from '@/repositories/in-memory/in-memory-pets-repository'

import { makePet } from '@/tests/factories/make-pet-factory'
import { makeOrg } from '@/tests/factories/make-org-factory'

import { ResourceNotFoundError } from '@/errors/resource-not-found-error'

import { RegisterPetUseCase } from './register-pet'

describe('Register pet use-case', () => {
  let orgsRepository: InMemoryOrgsRepository
  let petsRepository: InMemoryPetsRepository
  let sut: RegisterPetUseCase

  beforeEach(() => {
    orgsRepository = new InMemoryOrgsRepository()
    petsRepository = new InMemoryPetsRepository(orgsRepository)
    sut = new RegisterPetUseCase(petsRepository, orgsRepository)
  })

  it('should be able to register an pet', async () => {
    const password = '123456'
    const fakeOrg = makeOrg({
      password,
    })

    const createdOrg = await orgsRepository.create({
      ...fakeOrg,
      password_hash: await hash(password, 6),
    })

    const {
      name,
      description,
      age,
      dependency_level,
      energy_level,
      size,
      org_id,
    } = makePet({
      org_id: createdOrg.id,
    })

    const { pet } = await sut.execute({
      org_id,
      name,
      description,
      age,
      dependency_level,
      energy_level,
      size,
    })

    expect(pet.id).toEqual(expect.any(String))
    expect(petsRepository.pets).toHaveLength(1)
  })

  it('should not be able to register an pet with an inexistent org', async () => {
    const { name, description, age, dependency_level, energy_level, size } =
      makePet()

    await expect(() =>
      sut.execute({
        org_id: 'inexistent-org',
        name,
        description,
        age,
        dependency_level,
        energy_level,
        size,
      }),
    ).rejects.toBeInstanceOf(ResourceNotFoundError)
  })
})
