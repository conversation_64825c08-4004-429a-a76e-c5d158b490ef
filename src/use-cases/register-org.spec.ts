import { beforeEach, describe, expect, it } from 'vitest'
import { compare } from 'bcryptjs'

import { makeOrg } from '@/tests/factories/make-org-factory'
import { InMemoryOrgsRepository } from '@/repositories/in-memory/in-memory-orgs-repository'

import { OrgAlreadyRegisteredError } from '@/errors/org-already-registered'

import { RegisterOrgUseCase } from './register-org'

describe('Register org use-case', () => {
  let orgsRepository: InMemoryOrgsRepository
  let sut: RegisterOrgUseCase

  beforeEach(() => {
    orgsRepository = new InMemoryOrgsRepository()
    sut = new RegisterOrgUseCase(orgsRepository)
  })

  it('should be able to register an org', async () => {
    const { org } = await sut.execute(makeOrg())

    expect(orgsRepository.orgs).toHaveLength(1)
    expect(org.id).toEqual(expect.any(String))
  })

  it('should be able to hash the password', async () => {
    const password = '123456'

    const { org } = await sut.execute(
      makeOrg({
        password,
      }),
    )

    expect(await compare(password, org.password_hash)).toBeTruthy()
  })

  it('should not be able to register an org with same email twice', async () => {
    const fakeOrg = makeOrg()

    await sut.execute(fakeOrg)

    await expect(() => sut.execute(fakeOrg)).rejects.toBeInstanceOf(
      OrgAlreadyRegisteredError,
    )
  })
})
