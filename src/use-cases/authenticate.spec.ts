import { beforeEach, describe, expect, it } from 'vitest'
import { hash } from 'bcryptjs'

import { makeOrg } from '@/tests/factories/make-org-factory'
import { InMemoryOrgsRepository } from '@/repositories/in-memory/in-memory-orgs-repository'

import { InvalidCredentialsError } from '@/errors/invalid-credentials-error'

import { AuthenticateUseCase } from './authenticate'

describe('Authenticate use-case', () => {
  let orgsRepository: InMemoryOrgsRepository
  let sut: AuthenticateUseCase

  beforeEach(() => {
    orgsRepository = new InMemoryOrgsRepository()
    sut = new AuthenticateUseCase(orgsRepository)
  })

  it('should be able to authenticate', async () => {
    const password = '123456'

    const fakeOrg = makeOrg({
      password: '123346',
    })

    const org = await orgsRepository.create({
      ...fakeOrg,
      password_hash: await hash(password, 6),
    })

    const { org: authenticatedOrg } = await sut.execute({
      email: org.email,
      password,
    })

    expect(authenticatedOrg.id).toEqual(expect.any(String))
  })

  it('should not be able to authenticate with invalid e-mail', async () => {
    const password = '123456'

    const fakeOrg = makeOrg({
      password: '123346',
    })

    await orgsRepository.create({
      ...fakeOrg,
      password_hash: await hash(password, 6),
    })

    await expect(() =>
      sut.execute({
        email: '<EMAIL>',
        password,
      }),
    ).rejects.toBeInstanceOf(InvalidCredentialsError)
  })

  it('should not be able to authenticate with wrong password', async () => {
    const password = '123456'

    const fakeOrg = makeOrg({
      password: '123346',
    })

    const org = await orgsRepository.create({
      ...fakeOrg,
      password_hash: await hash(password, 6),
    })

    await expect(() =>
      sut.execute({
        email: org.email,
        password: 'wrong-password',
      }),
    ).rejects.toBeInstanceOf(InvalidCredentialsError)
  })
})
