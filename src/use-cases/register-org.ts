import type { Org } from 'generated/prisma'

import { hash } from 'bcryptjs'

import type { OrgsRepository } from '@/repositories/orgs-repository'

import { OrgAlreadyRegisteredError } from '@/errors/org-already-registered'

interface RegisterOrgUseCaseRequest {
  name: string
  owner_name: string
  email: string
  password: string

  state: string
  cep: string
  city: string
  neighborhood: string
  street: string
  latitude: number
  longitude: number

  whatsapp: string
}

interface RegisterOrgUseCaseResponse {
  org: Org
}

export class RegisterOrgUseCase {
  // eslint-disable-next-line prettier/prettier
  constructor(private orgsRepository: OrgsRepository) { }

  async execute({
    name,
    owner_name,
    email,
    password,
    state,
    cep,
    city,
    neighborhood,
    street,
    latitude,
    longitude,
    whatsapp,
  }: RegisterOrgUseCaseRequest): Promise<RegisterOrgUseCaseResponse> {
    const orgWithEmailRegistered = await this.orgsRepository.findByEmail(email)

    if (orgWithEmailRegistered) {
      throw new OrgAlreadyRegisteredError()
    }

    const password_hash = await hash(password, 6)

    const org = await this.orgsRepository.create({
      name,
      owner_name,
      email,
      password_hash,
      state,
      city,
      cep,
      neighborhood,
      street,
      latitude,
      longitude,
      whatsapp,
    })

    return {
      org,
    }
  }
}
