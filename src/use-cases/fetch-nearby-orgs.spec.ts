import { beforeEach, describe, expect, it } from 'vitest'

import { makeOrg } from '@/tests/factories/make-org-factory'
import { InMemoryOrgsRepository } from '@/repositories/in-memory/in-memory-orgs-repository'

import { FetchNearbyOrgsUseCase } from './fetch-nearby-orgs'

describe('Fetch nearby orgs use-case', () => {
  let orgsRepository: InMemoryOrgsRepository
  let sut: FetchNearbyOrgsUseCase

  beforeEach(() => {
    orgsRepository = new InMemoryOrgsRepository()
    sut = new FetchNearbyOrgsUseCase(orgsRepository)
  })

  it('should be able to fetch nearby orgs', async () => {
    const {
      name,
      owner_name,
      email,
      password,
      whatsapp,
      cep,
      city,
      neighborhood,
      state,
      street,
    } = makeOrg()

    const createdOrg = await orgsRepository.create({
      name,
      owner_name,
      email,
      whatsapp,
      cep,
      city,
      neighborhood,
      state,
      street,
      password_hash: password,
      latitude: -23.5455478,
      longitude: -46.9319517,
    })

    const { orgs } = await sut.execute({
      userLatitude: -23.5438573,
      userLongitude: -46.926016,
    })

    expect(orgs).toHaveLength(1)
    expect(orgs).toEqual([createdOrg])
  })
})
