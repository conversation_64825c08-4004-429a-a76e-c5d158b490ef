import type { DependencyL<PERSON>l, Pet } from 'generated/prisma'

import type { PetsRepository } from '@/repositories/pets-repository'
import type { OrgsRepository } from '@/repositories/orgs-repository'

import { ResourceNotFoundError } from '@/errors/resource-not-found-error'

interface RegisterPetRequest {
  name: string
  age: string
  description: string
  org_id: string
  dependency_level: DependencyLevel
  energy_level: string
  size: string
}

interface RegisterPetResponse {
  pet: Pet
}

export class RegisterPetUseCase {
  // eslint-disable-next-line prettier/prettier
  constructor(private petsRepository: PetsRepository, private orgsRepository: OrgsRepository) { }

  async execute({
    name,
    age,
    org_id,
    description,
    dependency_level,
    energy_level,
    size,
  }: RegisterPetRequest): Promise<RegisterPetResponse> {
    const org = await this.orgsRepository.findById(org_id)

    if (!org) {
      throw new ResourceNotFoundError()
    }

    const pet = await this.petsRepository.create({
      name,
      age,
      description,
      dependency_level,
      energy_level,
      size,
      org_id,
    })

    return {
      pet,
    }
  }
}
