import type { Pet } from 'generated/prisma'

import type { PetsRepository } from '@/repositories/pets-repository'

interface ListPetsUseCaseRequest {
  city: string
}

interface ListPetsUseCaseResponse {
  pets: Pet[]
}

export class ListPetsUseCase {
  // eslint-disable-next-line prettier/prettier
  constructor(private petsRepository: PetsRepository) { }

  async execute({
    city,
  }: ListPetsUseCaseRequest): Promise<ListPetsUseCaseResponse> {
    const pets = await this.petsRepository.findAllByCity(city)

    return {
      pets,
    }
  }
}
