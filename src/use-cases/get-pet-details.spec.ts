import { beforeEach, describe, expect, it } from 'vitest'
import { hash } from 'bcryptjs'

import { InMemoryPetsRepository } from '@/repositories/in-memory/in-memory-pets-repository'
import { InMemoryOrgsRepository } from '@/repositories/in-memory/in-memory-orgs-repository'

import { makeOrg } from '@/tests/factories/make-org-factory'
import { makePet } from '@/tests/factories/make-pet-factory'

import { ResourceNotFoundError } from '@/errors/resource-not-found-error'

import { GetPetDetailsUseCase } from './get-pet-details'

describe('List pets use-case', () => {
  let orgsRepository: InMemoryOrgsRepository
  let petsRepository: InMemoryPetsRepository
  let sut: GetPetDetailsUseCase

  beforeEach(() => {
    orgsRepository = new InMemoryOrgsRepository()
    petsRepository = new InMemoryPetsRepository(orgsRepository)
    sut = new GetPetDetailsUseCase(petsRepository)
  })

  it('should be able to get details from a pet', async () => {
    const {
      name,
      email,
      owner_name,
      cep,
      city,
      latitude,
      longitude,
      neighborhood,
      state,
      street,
      whatsapp,
      password,
    } = makeOrg({
      city: 'Itapevi',
    })

    const org = await orgsRepository.create({
      name,
      email,
      owner_name,
      cep,
      city,
      latitude,
      longitude,
      neighborhood,
      state,
      street,
      whatsapp,
      password_hash: await hash(password, 6),
    })

    const createdPet = await petsRepository.create({
      ...makePet({
        org_id: org.id,
      }),
    })

    const { pet } = await sut.execute({
      petId: createdPet.id,
    })

    expect(createdPet).toEqual(pet)
  })

  it('should not be able to get a non-existing pet', async () => {
    await expect(sut.execute({ petId: 'invalid' })).rejects.toBeInstanceOf(
      ResourceNotFoundError,
    )
  })
})
