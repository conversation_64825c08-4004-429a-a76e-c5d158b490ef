{"name": "find-a-friend-api", "version": "1.0.0", "engines": {"node": "20.10.0"}, "scripts": {"test": "vitest run --project unit", "test:e2e": "vitest run --project e2e", "test:watch": "vitest --project unit", "test:e2e:watch": "vitest --project e2e", "test:coverage": "vitest run --coverage", "start:dev": "tsx watch src/server.ts", "start": "node build/server.js", "build": "tsup src --out-dir build"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@fastify/cookie": "11.0.2", "@fastify/jwt": "9.1.0", "@prisma/client": "6.13.0", "bcryptjs": "3.0.2", "dotenv": "17.2.1", "fastify": "5.4.0", "zod": "4.0.15"}, "devDependencies": {"@faker-js/faker": "9.9.0", "@rocketseat/eslint-config": "2.2.2", "@types/bcryptjs": "3.0.0", "@types/node": "24.2.0", "@types/supertest": "6.0.3", "@vitest/coverage-v8": "3.2.4", "eslint": "8.57.1", "prisma": "6.13.0", "supertest": "7.1.4", "tsup": "8.5.0", "tsx": "4.20.3", "typescript": "5.9.2", "vite-tsconfig-paths": "5.1.4", "vitest": "3.2.4"}}